# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Core Architecture

ASI-Arch is an autonomous multi-agent framework for scientific research in linear attention mechanisms. The system consists of three main components working together:

### 1. Pipeline (`pipeline/`) - Autonomous Discovery Engine
The core orchestration system that runs continuous experiments through specialized agents:

- **`evolve/`**: Creative architecture generation
  - `planner`: Designs new model architectures
  - `deduplication`: Ensures novelty by checking against existing architectures
  - `code_checker`: Validates generated code correctness
  - `motivation`: Provides scientific rationale for architectural choices

- **`eval/`**: Empirical validation system
  - `trainer`: Handles model training processes
  - `debugger`: Automatically analyzes and fixes training errors

- **`analyse/`**: Results analysis and insight generation
  - `analyzer`: Comprehensive experiment result analysis

- **`database/`**: Data persistence interface connecting to MongoDB backend

### 2. Database (`database/`) - Experimental Memory
MongoDB-based storage system for all experimental data:

- **`mongodb_database.py`**: Core database client and operations
- **`candidate_manager.py`**: Manages top-performing architectures
- **`faiss_manager.py`**: Vector similarity search for deduplication
- **`evaluate_agent/`**: Architecture scoring and evaluation
- Accessible via REST API (`mongodb_api.py`)

### 3. Cognition Base (`cognition_base/`) - Knowledge Repository
RAG-powered research knowledge system:

- **`rag_service.py`**: Vector search over research papers
- **`rag_api.py`**: Flask API for knowledge queries
- **`cognition/`**: 100+ research papers as JSON knowledge base
- Uses OpenSearch for efficient document retrieval

## Development Commands

### Environment Setup
```bash
# Create conda environment
conda create -n asi-arch python=3.10
conda activate asi-arch

# Install dependencies
pip install -r requirements.txt
pip3 install torch==2.4.0 --index-url https://download.pytorch.org/whl/cu124
pip install -r database/requirements.txt
pip install -r cognition_base/requirements.txt
```

### Service Management
```bash
# Start database services (terminal 1)
cd database
docker-compose up -d
./start_api.sh

# Start cognition base services (terminal 2)
cd cognition_base
docker-compose up -d
python rag_api.py

# Run main pipeline (terminal 3)
cd pipeline
python pipeline.py
```

### Service Health Checks
```bash
# Check database API
curl http://localhost:8001/health

# Check cognition base API
curl http://localhost:5000/health

# MongoDB admin interface
open http://localhost:8081

# OpenSearch dashboard
open http://localhost:5601
```

## Key Configuration

### `pipeline/config.py`
Core configuration file containing:
- `SOURCE_FILE`: Target file for evolution
- `BASH_SCRIPT`: Training script path
- `RESULT_FILE`: Experiment results location
- `RAG`: Cognition base API URL
- `DATABASE`: MongoDB API URL
- `MAX_DEBUG_ATTEMPT`: Error recovery attempts
- `MAX_RETRY_ATTEMPTS`: Evolution retry limit

### Pipeline Workflow
The main execution loop (`pipeline.py`) follows this cycle:
1. **Sample**: Select promising parent architecture from database
2. **Evolve**: Generate new architecture variant using LLM agents
3. **Evaluate**: Train and benchmark the new architecture
4. **Analyze**: Extract insights and performance metrics
5. **Update**: Store results in database for future evolution

## Important Implementation Details

### Multi-Agent Coordination
- Each pipeline module uses specialized AI agents with distinct prompts
- Agents coordinate through structured data exchange via `DataElement` objects
- Error handling includes automatic debugging and retry mechanisms
- All agent interactions are logged via `utils.agent_logger`

### Async Architecture
- Pipeline uses `asyncio` for concurrent operations
- Database and cognition base APIs are accessed asynchronously
- Supports continuous experiment execution with error recovery

### Docker Dependencies
- Database requires MongoDB 7.0 container
- Cognition base requires OpenSearch 2.11.0 container
- Both services must be running before pipeline execution
- API services run on separate processes/terminals

### Data Flow
- Experiments stored as structured `DataElement` objects in MongoDB
- Knowledge retrieval through RAG queries to research paper corpus
- Vector similarity search prevents duplicate architecture generation
- Performance tracking through candidate management system

## Testing Architecture Discovery

To test a single evolution cycle:
```bash
cd pipeline
python -c "import asyncio; from pipeline import run_single_experiment; asyncio.run(run_single_experiment())"
```

Monitor logs in `pipeline/files/debug/` for debugging information.
Check experiment results in configured CSV files for performance metrics.