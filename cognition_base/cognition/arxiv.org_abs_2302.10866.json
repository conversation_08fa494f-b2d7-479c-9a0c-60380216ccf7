[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_1: Recurrence of Gating and Implicit Long Convolutions as a Subquadratic Attention Replacement\n\nHyena introduces a novel operator for sequence modeling that replaces self-attention with a stack (recurrence) of two subquadratic primitives: (1) data-controlled element-wise multiplicative gating and (2) implicitly parameterized long convolutions. Each layer alternates between these, with the depth of recurrence controlling expressivity. The long convolution filters are parameterized by a small feed-forward network (FFN) over positional encodings, allowing the model to decouple memory length from parameter count and achieve unbounded context with sublinear parameter scaling.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Improved modeling of long-range dependencies and context should manifest as higher lambada_openai, hellaswag, and winogrande scores, with smoother and lower training loss curves compared to quadratic attention, especially on long sequences.\n- Reasoning and factual tasks (boolq, arc_easy/challenge, openbookqa) should match or closely approach Transformer baselines, as the operator supports unrestricted context and high expressivity.\n- Commonsense and social reasoning (piqa, social_iqa) performance remains stable or improves due to better memory and context integration.\n- For very long contexts (e.g., squad_completion on long passages, swde), expect robust extraction and recall, with runtime and memory usage scaling subquadratically.\n- Data augmentation/few-shot generalization (fda) is preserved due to in-context learning.\n**Architectural_Symptoms**: \n- Training logs show faster convergence on long-sequence tasks, with memory and runtime scaling much better than attention-based models as sequence length increases.", "BACKGROUND": "**Title**: Hyena Hierarchy: Towards Larger Convolutional Language Models\n\n**Historical Technical Context**: Prior to this work, sequence modeling was dominated by architectures such as RNNs, LSTMs, CNNs, and especially Transformers. Transformers leverage self-attention, a mechanism that enables modeling of unrestricted dependencies within sequences by computing pairwise interactions between all tokens, but at quadratic computational cost with respect to sequence length. Earlier attempts to scale beyond attention used low-rank, sparse, or state-space models, but these typically required hybridization with attention layers to match Transformer performance.\n\n**Technical Limitations**: The main bottleneck of attention-based models is their quadratic time and memory complexity, which limits the feasible input sequence length and thus the accessible context. Subquadratic alternatives, such as explicit convolutions or state-space models, often suffer from limited expressivity or require parameter counts that scale with sequence length, failing to match Transformer-level quality on long-range tasks. This paper addresses the need for efficient, expressive operators that decouple parameter count from sequence length and enable unrestricted context.\n\n**Paper Concepts**: - **Implicit Long Convolution:** A convolutional operator where the filter is parameterized as a function (e.g., via a neural network), allowing filter length to exceed parameter count and capturing long-range dependencies efficiently.\n- **Data-Controlled Operator:** A linear transformation whose parameters are functions of the input sequence, generalizing the concept of attention (y = H(u)v) where H(u) is input-dependent.\n- **Multiplicative Gating:** An element-wise product between projected inputs, enabling dynamic modulation of information flow at each step of the sequence.\n- **Hyena Recurrence:** A hierarchical composition of gating and long convolutions, defined recursively to achieve subquadratic complexity and unrestricted context.\n\n**Experimental Context**: The paper evaluates models on a range of language modeling tasks emphasizing in-context learning, such as recall, reasoning, counting, and function induction, as well as open-ended generation. Performance is assessed both on synthetic mechanistic interpretability tasks and large-scale language modeling, focusing on the ability to process long sequences, generalize across contexts, and match or surpass attention-based architectures in accuracy and efficiency. The evaluation prioritizes both few-shot and zero-shot capabilities, reflecting the need for adaptable and scalable models.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- Replace each self-attention block with a stack of N alternating operations: (a) multiplicative gating by learned projections of the input, and (b) long 1D convolutions, where the convolution filter is generated by a small FFN applied to positional encodings and modulated by an exponential window. The recurrence is: \\( z_1 = v_t \\); for \\( n = 1..N \\), \\( z_{n+1} = x^n_t \\cdot (h^n * z_n)_t \\); output \\( y = z_{N+1} \\).\n**Key_Mechanism**: \n- The multiplicative gating allows dynamic, data-dependent control of information flow (akin to attention's data-controlled linearity), while the implicit long convolution enables efficient, flexible modeling of dependencies across arbitrary distances without parameter explosion or quadratic cost.\n**Mathematical_Formulation**: \n- For input \\( u \\), projections \\( v, x^1, ..., x^N \\), and filters \\( h^1, ..., h^N \\):\n  \\[\n  z_1 = v_t \\\\\n  z_{n+1} = x^n_t \\cdot (h^n * z_n)_t \\quad \\text{for } n=1..N \\\\\n  y = z_{N+1}\n  \\]\n  Each convolution is computed via FFT: \\( (h * u) = \\text{iFFT}(\\text{FFT}(h) \\cdot \\text{FFT}(u)) \\).\n  Filters: \\( h^n_t = \\text{Window}(t) \\cdot \\text{FFN}(\\text{PosEnc}(t)) \\).\n**Computational_Properties**: \n- Time complexity: \\( O(N L \\log L) \\) per layer (with N small, e.g., 2–4), subquadratic in sequence length.\n- Memory: No quadratic attention matrix; parameters scale sublinearly with context length.\n- Parallelization: FFT-based convolutions and FFN filter generation are highly parallelizable across sequence and batch.\n- Training efficiency: Lower memory footprint allows larger context or batch size; faster wall-clock training for long sequences.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- Replace each attention block in the Transformer with the Hyena operator: insert a stack of N alternating gating and long convolution modules in place of multi-head attention.\n- Use the same input/output shapes as attention for compatibility with standard residual and normalization layers.\n- Retain feed-forward (MLP) blocks between Hyena layers as in standard architectures.\n**Parameter_Settings**: \n- Number of recurrences N: typically 2–4 for strong expressivity.\n- FFN size for filter generation: small (e.g., 2–3 layers, width ~64–256).\n- Positional encoding: Sine/cosine or learned, dimension 8–32.\n- Exponential window decay parameter: tune per channel to regularize filter length.\n- Initialize FFN weights using standard small-variance schemes; window bias to avoid zeroing filters.\n**Application_Conditions**: \n- Use when sequence lengths exceed 2K or when memory/runtime of attention is prohibitive.\n- Especially beneficial for tasks requiring long-context recall, in-context learning, or efficient scaling to large batches/sequences.\n**Expected_Outcomes**: \n- Expect training loss to decrease more smoothly on long-sequence tasks, with perplexity and downstream metric parity or improvement over attention for long-range and context-heavy benchmarks (lambada_openai, hellaswag, winogrande, squad_completion, swde).\n- Compute and memory cost scale subquadratically, enabling larger context windows without hardware bottlenecks."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_2: Implicitly Parameterized Convolution Filters via FFN over Positional Encodings for Decoupling Memory from Parameter Count\n\n<PERSON><PERSON><PERSON>'s long convolution filters are not learned as explicit parameter vectors (which would scale linearly with context length), but are instead generated on-the-fly by applying a small feed-forward network (FFN) to positional encodings, optionally modulated by an exponential window. This implicit parameterization enables the filter to have length equal to the sequence, supporting unbounded context and capturing both low- and high-frequency patterns, while keeping parameter count independent of sequence length.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Models with implicit FFN-based filters maintain or improve performance as sequence length and vocabulary size increase, as seen in associative recall, lambada_openai, squad_completion, and swde.\n- Training loss and perplexity remain stable even as sequence length scales, with less degradation than explicit convolution or state-space models.\n- On tasks requiring fine-grained or multi-scale context integration (e.g., winogrande, piqa, social_iqa), expect improved or stable performance as sequence grows.\n**Architectural_Symptoms**: \n- No parameter growth with increased context; model size remains constant, but ability to model long dependencies improves.", "BACKGROUND": "**Title**: Hyena Hierarchy: Towards Larger Convolutional Language Models\n\n**Historical Technical Context**: Prior to this work, sequence modeling was dominated by architectures such as RNNs, LSTMs, CNNs, and especially Transformers. Transformers leverage self-attention, a mechanism that enables modeling of unrestricted dependencies within sequences by computing pairwise interactions between all tokens, but at quadratic computational cost with respect to sequence length. Earlier attempts to scale beyond attention used low-rank, sparse, or state-space models, but these typically required hybridization with attention layers to match Transformer performance.\n\n**Technical Limitations**: The main bottleneck of attention-based models is their quadratic time and memory complexity, which limits the feasible input sequence length and thus the accessible context. Subquadratic alternatives, such as explicit convolutions or state-space models, often suffer from limited expressivity or require parameter counts that scale with sequence length, failing to match Transformer-level quality on long-range tasks. This paper addresses the need for efficient, expressive operators that decouple parameter count from sequence length and enable unrestricted context.\n\n**Paper Concepts**: - **Implicit Long Convolution:** A convolutional operator where the filter is parameterized as a function (e.g., via a neural network), allowing filter length to exceed parameter count and capturing long-range dependencies efficiently.\n- **Data-Controlled Operator:** A linear transformation whose parameters are functions of the input sequence, generalizing the concept of attention (y = H(u)v) where H(u) is input-dependent.\n- **Multiplicative Gating:** An element-wise product between projected inputs, enabling dynamic modulation of information flow at each step of the sequence.\n- **Hyena Recurrence:** A hierarchical composition of gating and long convolutions, defined recursively to achieve subquadratic complexity and unrestricted context.\n\n**Experimental Context**: The paper evaluates models on a range of language modeling tasks emphasizing in-context learning, such as recall, reasoning, counting, and function induction, as well as open-ended generation. Performance is assessed both on synthetic mechanistic interpretability tasks and large-scale language modeling, focusing on the ability to process long sequences, generalize across contexts, and match or surpass attention-based architectures in accuracy and efficiency. The evaluation prioritizes both few-shot and zero-shot capabilities, reflecting the need for adaptable and scalable models.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- For each convolutional filter in the Hyena operator, instead of learning a parameter vector of length L, learn a small FFN that takes as input a positional encoding (e.g., sine/cosine, learned) and outputs the filter value for each position 0..L-1. The output is further modulated by a window function (e.g., exponential decay).\n**Key_Mechanism**: \n- This allows the model to generate arbitrarily long filters (matching input length) with a fixed number of parameters, supporting unbounded memory and capturing both global and local patterns. The FFN can learn high-frequency or decaying components as needed for the task, overcoming the low-frequency bias of standard neural nets.\n**Mathematical_Formulation**: \n- For filter \\( h \\) and position \\( t \\):\n  \\[\n  h_t = \\text{Window}(t) \\cdot \\text{FFN}(\\text{PosEnc}(t))\n  \\]\n  Where Window(t) is e.g., \\( \\exp(-\\beta t) \\), with \\(\\beta\\) learned or fixed per channel.\n**Computational_Properties**: \n- Parameter cost: O(1) with respect to sequence length.\n- Filter generation: O(L) per filter, highly parallelizable.\n- Supports causal and non-causal filters as needed.\n- Enables efficient hardware utilization via batched FFN evaluation.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- In each Hyena layer, replace explicit convolution filters or state-space parameterizations with an FFN that generates the filter weights for each position via positional encoding.\n- Apply a window function (e.g., exponential decay, with learnable parameters) to regularize filter length and encourage multi-scale specialization.\n**Parameter_Settings**: \n- FFN: 2–3 layers, width 64–256; use sine activations for high-frequency expressivity.\n- Positional encoding: sine/cosine or learned, dimension 8–32.\n- Window decay parameter: learnable per channel, with optional bias to avoid zeroing.\n**Application_Conditions**: \n- Use when sequence length is variable or very large, or when model size must be constrained.\n- Particularly effective for tasks where memory of distant tokens is critical, such as long-context reading comprehension, recall, and structured data extraction.\n**Expected_Outcomes**: \n- Stable or improved performance on long-sequence benchmarks (lambada_openai, squad_completion, swde) without parameter growth.\n- Training is robust to increases in context window, enabling scaling to hundreds of thousands of tokens.\n- Efficient hardware utilization and parallel filter generation."}]