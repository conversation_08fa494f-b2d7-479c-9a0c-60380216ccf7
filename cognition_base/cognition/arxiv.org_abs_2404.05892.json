[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_1: Multi-Headed Matrix-Valued States with Per-Head LayerNorm and Gating (Eagle/RWKV-5)\n\nEagle introduces multi-headed matrix-valued recurrent states, replacing the vector-valued states of RWKV-4. Each attention head maintains its own D/h × D/h matrix-valued state, enabling richer token interaction modeling within each head. The architecture applies per-head LayerNorm and a SiLU-based gating mechanism, increasing expressivity and stability while preserving O(1) inference complexity per token.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: Expect smoother and lower training loss curves, especially for long-context tasks. Improvements should be notable in lambada_openai (narrative/context understanding), hellaswag (commonsense completion), winogrande (pronoun/context resolution), and squad_completion (reading comprehension), with secondary benefits on boolq, arc_easy/challenge, and openbookqa due to enhanced representational capacity. Training and inference efficiency (speed/memory) remain similar to prior RWKV.\n\n**Architectural_Symptoms**: Models with this feature show improved loss at later sequence positions (longer contexts), and higher accuracy on associative recall and long-context benchmarks, without a tradeoff in computational efficiency.", "BACKGROUND": "**Title**: Eagle and Finch: RWKV with Matrix-Valued States and Dynamic Recurrence\n\n**Historical Technical Context**: Prior to this work, sequence modeling was dominated by RNNs (including LSTM/GRU) with O(1) step-wise inference, and Transformers, which use multi-headed self-attention for high expressivity but suffer from O(N²) time/memory with sequence length. Recent advances, such as RWKV-4 and linear attention models, sought to blend RNN efficiency with Transformer-like modeling capacity by introducing decay-based or linearized attention mechanisms, enabling parallel training and efficient inference. These architectures typically relied on vector-valued recurrent states and fixed, learned decay schedules for memory updates.\n\n**Technical Limitations**: Classic RNNs struggled with parallelizable training and long-range dependencies, while Transformers incurred prohibitive computational costs for long sequences. Linear attention and early RWKV models improved efficiency but were limited by the expressiveness of vector-valued states and static decay rates, restricting their ability to model complex, context-dependent memory dynamics. These constraints motivated the search for architectures that could achieve both high modeling power and scalable, efficient inference.\n\n**Paper Concepts**: - **Matrix-Valued States**: Recurrent states represented as matrices (not vectors), allowing richer and multi-headed memory representations per time step.\n- **Dynamic Recurrence**: Data-dependent, time-varying decay rates \\( w_t \\) in the recurrent update, enabling the model to flexibly modulate memory retention based on input.\n- **Token Shift**: A learnable linear interpolation between current and previous token representations, controlling information flow per channel and head.\n- **LoRA Augmentation**: Low-rank adaptation functions \\( \\text{LoRA}(x) = \\lambda + \\tanh(xA)B \\) efficiently introduce input-conditioned parameter offsets for dynamic behavior.\n- **Time Mixing**: The mechanism by which current and past token information are combined using learned or data-dependent decay, generalizing attention and recurrence.\n\n**Experimental Context**: The models are evaluated on a wide range of language understanding and generation tasks, including commonsense reasoning, reading comprehension, question answering, and long-context recall. Evaluation emphasizes both accuracy and efficiency across multilingual, code, and multimodal domains, with careful attention to performance on long sequences and associative recall. The experimental philosophy focuses on demonstrating both competitive accuracy and superior scalability compared to traditional attention-based models.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: For each attention head, maintain a matrix-valued recurrent state S ∈ ℝ^(D/h × D/h) updated at each timestep via decayed, weighted sums of key-value outer products. Apply per-head LayerNorm before gating and output projection. Use a SiLU gate on the output of the state-mixing operation to modulate information flow.\n\n**Key_Mechanism**: Matrix-valued states allow each head to capture higher-order and cross-channel interactions within its subspace, improving the ability to model complex token relationships and long-range dependencies. Per-head normalization and gating stabilize training and enable more nuanced control over information propagation.\n\n**Mathematical_Formulation**:\n- State update:  \n  s′ = diag(w) · s + kᵗ·v  \n  (w is a learned contraction vector, k and v are key/value projections)\n- Output:  \n  o_t = concat(SiLU(g_t) ⊙ LayerNorm(r_t · wkv_t)) · W_o\n\n**Computational_Properties**:  \n- Inference: O(1) per token (like RNNs)  \n- Training: O(N) per sequence, fully parallelizable  \n- Memory: Linear in sequence length, per-head state storage  \n- Efficient on both GPU and CPU, similar to prior RWKV", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- Replace vector-valued recurrent state in each time-mixing block with a D/h × D/h matrix per head.\n- Insert per-head LayerNorm and SiLU gate before output projection.\n- Keep channel-mixing and token-shift modules as in RWKV-4, with minor hidden size reduction to balance parameter count.\n\n**Parameter_Settings**:  \n- Number of heads: h ∈ [4, 32], scaling with model dimension  \n- Matrix state dimension: D/h × D/h  \n- Initialize decay parameters ω so that w = exp(−exp(ω)) ∈ (0,1)  \n- SiLU gating: standard initialization  \n- LayerNorm: per-head/group normalization\n\n**Application_Conditions**:  \n- Apply when long context retention, narrative flow, or associative recall are bottlenecks in existing models.\n- Especially beneficial for tasks requiring complex token dependencies or when scaling to longer contexts.\n\n**Expected_Outcomes**:  \n- Smoother, lower training loss curves, especially at high sequence positions.\n- Notable improvements on lambada_openai, hellaswag, winogrande, and squad_completion.\n- Maintains fast inference and efficient memory usage; no quadratic scaling."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_2: Dynamic Data-Dependent Recurrence via LoRA-Augmented Decay and Token Shift (Finch/RWKV-6)\n\nFinch extends Eagle by making both the decay schedule (memory retention) and token shift operations data-dependent. This is achieved by parameterizing the decay vector and token shift interpolation with small, trainable weight matrices (LoRA), allowing the model to dynamically modulate memory and information flow based on input content at each timestep.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**:  \n- Stronger improvements in lambada_openai, squad_completion, and associative recall (MQAR), especially at very long sequence lengths.\n- Enhanced performance on reasoning tasks (boolq, arc_easy/challenge, openbookqa) due to more flexible context integration.\n- Training loss continues to decrease deeper into long sequences (see loss vs. position plots).\n- Memory and speed efficiency comparable to Eagle, with slight overhead for LoRA matrices.\n\n**Architectural_Symptoms**:  \n- Model exhibits less degradation in loss or accuracy for tokens far from the sequence start.\n- Outperforms static-decay models on long-context and in-context learning benchmarks.", "BACKGROUND": "**Title**: Eagle and Finch: RWKV with Matrix-Valued States and Dynamic Recurrence\n\n**Historical Technical Context**: Prior to this work, sequence modeling was dominated by RNNs (including LSTM/GRU) with O(1) step-wise inference, and Transformers, which use multi-headed self-attention for high expressivity but suffer from O(N²) time/memory with sequence length. Recent advances, such as RWKV-4 and linear attention models, sought to blend RNN efficiency with Transformer-like modeling capacity by introducing decay-based or linearized attention mechanisms, enabling parallel training and efficient inference. These architectures typically relied on vector-valued recurrent states and fixed, learned decay schedules for memory updates.\n\n**Technical Limitations**: Classic RNNs struggled with parallelizable training and long-range dependencies, while Transformers incurred prohibitive computational costs for long sequences. Linear attention and early RWKV models improved efficiency but were limited by the expressiveness of vector-valued states and static decay rates, restricting their ability to model complex, context-dependent memory dynamics. These constraints motivated the search for architectures that could achieve both high modeling power and scalable, efficient inference.\n\n**Paper Concepts**: - **Matrix-Valued States**: Recurrent states represented as matrices (not vectors), allowing richer and multi-headed memory representations per time step.\n- **Dynamic Recurrence**: Data-dependent, time-varying decay rates \\( w_t \\) in the recurrent update, enabling the model to flexibly modulate memory retention based on input.\n- **Token Shift**: A learnable linear interpolation between current and previous token representations, controlling information flow per channel and head.\n- **LoRA Augmentation**: Low-rank adaptation functions \\( \\text{LoRA}(x) = \\lambda + \\tanh(xA)B \\) efficiently introduce input-conditioned parameter offsets for dynamic behavior.\n- **Time Mixing**: The mechanism by which current and past token information are combined using learned or data-dependent decay, generalizing attention and recurrence.\n\n**Experimental Context**: The models are evaluated on a wide range of language understanding and generation tasks, including commonsense reasoning, reading comprehension, question answering, and long-context recall. Evaluation emphasizes both accuracy and efficiency across multilingual, code, and multimodal domains, with careful attention to performance on long sequences and associative recall. The experimental philosophy focuses on demonstrating both competitive accuracy and superior scalability compared to traditional attention-based models.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**:  \n- The decay vector w_t at each timestep is generated by a lightweight neural function of the current and previous inputs via LoRA-augmented linear layers.\n- Token shift interpolation coefficients are also computed dynamically per token using similar LoRA-based functions.\n- Both mechanisms allow the model to adaptively control how much information to retain or forget at each position, based on local context.\n\n**Key_Mechanism**:  \n- By making memory retention and token mixing data-dependent, the model can adaptively prioritize relevant information and suppress irrelevant context, improving both long-range recall and context-sensitive reasoning.\n\n**Mathematical_Formulation**:  \n- Data-dependent lerp:  \n  ddlerp(a, b) = a + (b − a) ⊙ lora(a + (b − a) ⊙ μ_x)\n- LoRA function:  \n  lora(x) = λ + tanh(xA)B\n- Dynamic decay:  \n  d_t = lora_d(ddlerp_d(x_t, x_{t−1}))  \n  w_t = exp(−exp(d_t))\n- State update:  \n  s′ = diag(w_t)·s + kᵗ·v\n\n**Computational_Properties**:  \n- Inference: O(1) per token, with small additional cost for LoRA projections  \n- Training: O(N) per sequence, fully parallelizable  \n- Memory: Slightly increased per-layer parameter count, but still linear in sequence length  \n- Efficient parallelization; LoRA matrices are small relative to main model weights", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- Replace static decay and token shift in Eagle with LoRA-parameterized, data-dependent functions as described above.\n- Insert additional small weight matrices (A, B) for each relevant submodule (decay, token shift).\n- Retain the matrix-valued state and per-head LayerNorm/gating from Eagle.\n\n**Parameter_Settings**:  \n- LoRA matrices: A ∈ ℝ^(D×32), B ∈ ℝ^(32×D) (or larger for higher capacity models)\n- μ_x, λ: trainable vectors per channel\n- Initialization: Standard for LoRA; ensure output scales are compatible with main model activations\n- Dynamic decay is bounded in (0,1) via double-exponential parameterization\n\n**Application_Conditions**:  \n- Use when further gains are needed in long-context retention, in-context learning, or when static memory decay is a limiting factor.\n- Particularly advantageous for tasks with highly variable or unpredictable context dependencies.\n\n**Expected_Outcomes**:  \n- Superior performance on long-context, associative recall, and context-sensitive reasoning tasks (lambada_openai, squad_completion, MQAR, arc_easy/challenge).\n- Training loss continues to decrease at late sequence positions.\n- Maintains efficient scaling in memory and compute; suitable for deployment at scale."}]