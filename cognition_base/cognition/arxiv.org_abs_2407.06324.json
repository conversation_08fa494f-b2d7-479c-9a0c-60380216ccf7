[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_1: Seamless Hybridization of Eidetic and Fading Memory via Innovation Selection\n\nB’MOJO introduces a composable module that unifies short-term eidetic memory (exact recall over a finite window, as in attention) and long-term fading memory (state-based, as in SSMs) within a single architecture. The key novelty is the **Innovation Selection** mechanism: the model appends to the eidetic memory only those tokens that are unpredictable given the fading memory’s state, enabling the model to dynamically and efficiently allocate memory resources to the most informative past events, rather than by recency or fixed window size.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Substantial improvements on long-context tasks (e.g., lambada_openai, swde, squad_completion, PG-19) and associative recall benchmarks, with recall accuracy increasing as more eidetic memory tokens are allocated—especially as sequence length grows beyond training context.\n- Training loss decreases more smoothly and continues improving as context length increases, with better length generalization (perplexity drops or remains stable as context increases, unlike standard transformers).\n- Short-context tasks (boolq, piqa, arc_easy/challenge) remain stable or slightly improved compared to SSMs, but may not match full-attention transformers if context is very short.\n\n**Architectural_Symptoms**: \n- Models exhibit non-saturating scaling laws: adding more memory or compute leads to continued improvements, particularly for tasks requiring long-range recall.", "BACKGROUND": "**Title**: B’MOJO: Hybrid State Space Realizations of Foundation Models with Eidetic and Fading Memory\n\n**Historical Technical Context**: Prior to B’MOJO, dominant sequence modeling architectures included Transformers, which use attention mechanisms for finite-span, lossless (\"eidetic\") memory, and State Space Models (SSMs) such as Mamba, which use fixed-dimensional, lossy (\"fading\") recurrent states to capture long-range dependencies. Hybrid models have recently combined these approaches, but with rigid or inefficient mechanisms for balancing and scaling memory types. These architectures were shaped by advances in deep learning, stochastic realization theory, and efficient hardware implementations for attention and recurrence.\n\n**Technical Limitations**: Transformers are constrained by fixed context windows, making them inefficient for tasks requiring recall beyond this span, while SSMs cannot perform exact recall due to their inherently lossy memory. Previous hybrids cannot dynamically modulate or expand eidetic memory, nor efficiently unify fading and eidetic memory under resource constraints. These limitations hinder efficient, scalable, and precise transductive inference on long or unpredictable sequences.\n\n**Paper Concepts**: - **Eidetic Memory:** Lossless storage of selected past tokens (e.g., sliding window or context), enabling exact recall over a finite, possibly growing, span.\n- **Fading Memory:** Compressed, lossy summary of all past inputs in a fixed-size recurrent state, typically realized as \\( x_{t+1} = A(u_t)x_t + B(u_t) \\).\n- **Innovation Selection:** Mechanism that appends tokens to eidetic memory when their prediction error (innovation) exceeds a threshold, enabling adaptive memory growth.\n- **Transductive Inference:** Test-time computation that leverages all available data, not just what is encoded in model weights, emphasizing memorization and sample-specific reasoning.\n- **Bilinear Realization:** State space update where dynamics depend linearly on both state and input, enabling efficient and expressive sequence modeling.\n\n**Experimental Context**: B’MOJO is evaluated on tasks requiring both precise recall and generalization, including associative recall, language generation, question answering, and reasoning over both short and long contexts. Evaluation focuses on the ability to process and recall information from sequences much longer than those seen during training, as well as efficiency and scalability in training and inference. The philosophy emphasizes both inductive (generalization) and transductive (memorization and adaptation) performance.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- At each timestep, the model maintains a fixed-size fading memory (SSM state), a short-term eidetic window (sliding attention), and a dynamically growing eidetic memory buffer.\n- For each new token, compute the prediction error (“innovation”) using the SSM’s state; if the error exceeds a threshold (i.e., the token is not well-predicted), append the token to the eidetic memory.\n- Model output at each step is computed by attending over the concatenation of the current sliding window, the SSM state, and the eidetic memory.\n\n**Key_Mechanism**: \n- By storing only unpredictable tokens, the model focuses its high-fidelity memory on information that cannot be compressed into the fading state, maximizing recall efficiency and enabling exact retrieval of rare or important events even at long time horizons.\n\n**Mathematical_Formulation**: \n- State update: \\( x_t = A(u_t) x_{t-1} + B(u_t) \\)\n- Output: \\( y_t = C(u_t) x_t \\)\n- Innovation: \\( \\epsilon_t = \\text{error}(\\hat{y}(y_{t-1:t-k}), y_t) \\)\n- Eidetic memory update: \n  \\[\n  M_t = \\begin{cases}\n    M_{t-1} \\cup \\{u_t, \\epsilon_t\\}, & \\text{if } \\epsilon_t > \\min_{\\epsilon \\in M_{t-1}}(\\epsilon) \\\\\n    M_{t-1}, & \\text{otherwise}\n  \\end{cases}\n  \\]\n- Output: \\( \\text{Attn}(u_{t-w:t}, y_{t-w}, M_t) \\)\n\n**Computational_Properties**: \n- Time complexity per step is dominated by attention over the combined window and memory, plus SSM state update (efficient, as in Mamba).\n- Memory use grows with the number of unpredictable tokens, but in practice remains bounded by hardware or design constraints.\n- Amenable to parallelization via chunking, as fading and eidetic memory can be summarized per chunk for efficient batched computation.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- Replace or augment standard Transformer/SSM blocks with B’MOJO modules: insert the innovation selection logic after the SSM state update, before attention.\n- Eidetic memory buffer can be implemented as a prioritized queue or register array, updated asynchronously.\n- For efficient training, process input in chunks: summarize fading and eidetic memory at chunk boundaries and use sliding window attention over interleaved input/memory tokens.\n\n**Parameter_Settings**: \n- Key hyperparameters: size of fading memory (state dimension), sliding window size, eidetic memory capacity, innovation threshold.\n- Innovation threshold and eidetic memory size can be tuned to trade off between recall accuracy and compute/memory usage.\n- Initialization: SSM and attention weights as per standard practice; innovation predictor can be a simple fixed function (e.g., causal convolution) or a lightweight learned module.\n\n**Application_Conditions**: \n- Most beneficial when evaluation tasks require recall of information beyond the typical attention window, or when input distributions are non-stationary and unpredictable events are important.\n- Use when training loss plateaus on long-context tasks, or when memory/computation constraints preclude full attention over long sequences.\n\n**Expected_Outcomes**: \n- Improved long-range recall and context understanding (lambada_openai, swde, squad_completion, PG-19), with smooth training loss curves and better length generalization.\n- Comparable or improved computational efficiency versus SSMs and hybrids, with training/inference speed advantages at scale.\n- Stable or slightly improved performance on short-context reasoning tasks, with the main gains appearing as sequence length increases."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_2: Modular, Stackable Bilinear State Space Realization with Controllable Canonical Form\n\nB’MOJO generalizes both attention (Transformers) and SSMs (Mamba) by adopting a **controllable canonical form** for the state transition, enabling the model to efficiently and flexibly interpolate between pure attention (eidetic, exact recall), pure SSM (fading, lossy recall), and hybrid memory regimes. This modular design supports efficient stacking and scaling in hardware, with each layer able to independently modulate its memory type and span.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Training loss and perplexity scale smoothly and do not saturate as model size or memory allocation increases, especially on language modeling and long-context tasks.\n- For tasks requiring both local context and long-range dependencies (e.g., hellaswag, winogrande, squad_completion, swde), performance remains robust as context grows, with no sharp drop-off as in standard transformers.\n- Computational throughput is higher than comparable SSM/attention hybrids, enabling faster training and inference at scale.\n\n**Architectural_Symptoms**: \n- Models can be scaled to longer context lengths and larger parameter counts without architectural bottlenecks or memory inefficiency.", "BACKGROUND": "**Title**: B’MOJO: Hybrid State Space Realizations of Foundation Models with Eidetic and Fading Memory\n\n**Historical Technical Context**: Prior to B’MOJO, dominant sequence modeling architectures included Transformers, which use attention mechanisms for finite-span, lossless (\"eidetic\") memory, and State Space Models (SSMs) such as Mamba, which use fixed-dimensional, lossy (\"fading\") recurrent states to capture long-range dependencies. Hybrid models have recently combined these approaches, but with rigid or inefficient mechanisms for balancing and scaling memory types. These architectures were shaped by advances in deep learning, stochastic realization theory, and efficient hardware implementations for attention and recurrence.\n\n**Technical Limitations**: Transformers are constrained by fixed context windows, making them inefficient for tasks requiring recall beyond this span, while SSMs cannot perform exact recall due to their inherently lossy memory. Previous hybrids cannot dynamically modulate or expand eidetic memory, nor efficiently unify fading and eidetic memory under resource constraints. These limitations hinder efficient, scalable, and precise transductive inference on long or unpredictable sequences.\n\n**Paper Concepts**: - **Eidetic Memory:** Lossless storage of selected past tokens (e.g., sliding window or context), enabling exact recall over a finite, possibly growing, span.\n- **Fading Memory:** Compressed, lossy summary of all past inputs in a fixed-size recurrent state, typically realized as \\( x_{t+1} = A(u_t)x_t + B(u_t) \\).\n- **Innovation Selection:** Mechanism that appends tokens to eidetic memory when their prediction error (innovation) exceeds a threshold, enabling adaptive memory growth.\n- **Transductive Inference:** Test-time computation that leverages all available data, not just what is encoded in model weights, emphasizing memorization and sample-specific reasoning.\n- **Bilinear Realization:** State space update where dynamics depend linearly on both state and input, enabling efficient and expressive sequence modeling.\n\n**Experimental Context**: B’MOJO is evaluated on tasks requiring both precise recall and generalization, including associative recall, language generation, question answering, and reasoning over both short and long contexts. Evaluation focuses on the ability to process and recall information from sequences much longer than those seen during training, as well as efficiency and scalability in training and inference. The philosophy emphasizes both inductive (generalization) and transductive (memorization and adaptation) performance.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- Each B’MOJO layer uses a state update of the form:\n  \\[\n  x_{t+1} = A(u_t) x_t + B(u_t)\n  \\]\n  where \\( A(u_t) \\) is block upper-triangular, with identity matrices in the upper diagonal blocks (for sliding window/attention), and learned parameters in the lower rows (for fading memory).\n- Output is computed as a function of both the state and a non-linear readout (attention over the window and memory).\n- By tuning the structure of \\( A(u_t) \\) and the size of the attention window, each layer can act as a transformer, SSM, or a blend.\n\n**Key_Mechanism**: \n- The controllable canonical form ensures minimal realization (smallest state dimension for a given memory span and function), maximizing computational efficiency and expressivity.\n- Modular stacking allows easy scaling and composability, with each block able to specialize in different memory and reasoning functions.\n\n**Mathematical_Formulation**: \n- State update: as above, with \\( A(u_t) \\) structured as:\n  \\[\n  A_{B'MOJO}(u_t) =\n  \\begin{bmatrix}\n    0 & I & \\dots & 0 \\\\\n    \\vdots & \\ddots & \\ddots & \\vdots \\\\\n    0 & \\dots & 0 & I \\\\\n    a_1(u_t) & \\dots & a_N(u_t)\n  \\end{bmatrix}\n  \\]\n- Output: \\( y_t = \\rho(u_t, x_t) \\), with \\( \\rho \\) a non-linear function (e.g., softmax attention over concatenated window and memory).\n\n**Computational_Properties**: \n- Time complexity per step is linear in window and memory size; state update is efficient (as in Mamba), and attention is restricted to manageable spans.\n- Highly parallelizable via chunking and batched memory summarization.\n- Hardware-friendly due to fixed-size state and modular memory management.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- Implement as a drop-in replacement for transformer or SSM layers, with configurable parameters for attention window, state size, and memory type.\n- Stack multiple B’MOJO blocks, optionally mixing different memory configurations per layer for task specialization.\n\n**Parameter_Settings**: \n- Choose state dimension and window size based on task memory requirements and hardware constraints.\n- Tune block structure of \\( A(u_t) \\) to balance fading and eidetic memory per layer.\n- Use standard initialization for SSM/attention weights; minimal tuning needed for stability.\n\n**Application_Conditions**: \n- Ideal for large-scale LLMs where both local and global context are important, and where inference throughput and hardware efficiency are priorities.\n- Use when scaling to longer context lengths, or when existing models saturate in performance with increased compute.\n\n**Expected_Outcomes**: \n- Smooth scaling of language modeling and long-context task performance, with no bottlenecks as memory or parameter count increases.\n- Faster training and inference than hybrid SSM/attention models, with equal or better recall and reasoning performance.\n- Robustness to task variation, as layers can specialize in different memory spans and types."}]