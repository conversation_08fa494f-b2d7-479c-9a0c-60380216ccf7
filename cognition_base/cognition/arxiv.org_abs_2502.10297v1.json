[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_HIGH: Tunable Expressivity-Efficiency Tradeoff via Householder Product State-Transition Matrices in Linear RNNs", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**:  \n- Increasing the number of Householder transformations per token (parameter nh) in the state-transition matrix leads to marked improvements in tasks requiring long-range state-tracking and reasoning (e.g., lambada_openai, hellaswag, winogrande, arc_easy/challenge, openbookqa), as well as enhanced length extrapolation (lower and more stable training loss and perplexity far beyond training context).\n- Gains are particularly prominent on tasks with complex sequential dependencies (e.g., group word problems, narrative completion, factual QA), while tasks relying on local context or short dependencies (e.g., swde, piqa) see stable or marginal improvements.\n- Training loss curves display smoother convergence and reduced degradation on longer sequences as nh increases.\n\n**Architectural_Symptoms**:  \n- When increasing nh, hidden state effective rank remains stable or decays appropriately over long sequences, preventing the rank inflation and information loss seen in simpler (rank-1/diagonal) models.", "BACKGROUND": "**Title**: DeltaProduct: Improving State-Tracking in Linear RNNs via Householder Products\n\n**Historical Technical Context**: Prior to DeltaProduct, sequence modeling was dominated by architectures like RNNs and LSTMs, which processed data sequentially, and Transformers, which used self-attention for parallelism but suffered from quadratic complexity. Linear RNNs emerged as efficient alternatives, with models such as Mamba and GLA using diagonal state-transition matrices for fast, linear-time inference, but limited in expressivity. Recent advances included DeltaNet and RWKV-7, which introduced diagonal plus rank-1 state-transition matrices to improve token and channel mixing.\n\n**Technical Limitations**: Diagonal state-transition matrices in linear RNNs enable efficient computation but restrict the model’s ability to capture complex dependencies and solve challenging state-tracking tasks, such as group word problems. Even with diagonal plus rank-1 updates, models like DeltaNet are limited in expressivity, often requiring multiple layers to approximate more complex operations. Full, dense matrices offer maximal expressivity but are computationally prohibitive and unstable for long sequences.\n\n**Paper Concepts**: - **Generalized Householder Transformation**: A matrix of the form \\( I - \\beta k k^\\top \\), where \\( k \\) is a unit vector and \\( \\beta \\) controls the transformation, used to update the hidden state in a stable, norm-bounded way.\n- **DeltaProduct**: A linear RNN architecture whose state-transition matrix is a product of \\( n_h \\) generalized Householder transformations, yielding a diagonal plus rank-\\( n_h \\) structure that balances expressivity and efficiency.\n- **State-Tracking**: The model’s ability to track the evolution of a hidden state under a sequence of updates, often formalized as solving monoid or group word problems (e.g., permutation groups \\( S_n \\)).\n- **Length Extrapolation**: The model’s ability to generalize to sequences longer than those seen during training, crucial for robust language modeling.\n\n**Experimental Context**: The paper evaluates models on tasks requiring state-tracking (such as group word problems), language modeling, and reasoning, emphasizing the ability to maintain and update hidden states over long sequences. Evaluation philosophy focuses on both in-distribution performance and out-of-distribution generalization, particularly length extrapolation. Metrics include accuracy on sequence-based reasoning and cross-entropy loss for language generation and comprehension.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**:  \n- Replace the standard diagonal or diagonal-plus-rank-1 state-transition matrix in a linear RNN with a product of nh generalized Householder transformations per token, where each Householder is parameterized by a token-dependent key, value, and step size (beta).\n- Each token update thus executes nh sequential online gradient descent steps on a quadratic associative recall loss, yielding a state-transition matrix of the form:  \n  \\( A(x_i) = \\prod_{j=1}^{n_h} (I - \\beta_{i,j} k_{i,j} k_{i,j}^\\top) \\)\n- This construction interpolates between diagonal (nh=0/1), low-rank (small nh), and dense (large nh) state-transition matrices, with spectral norm guaranteed ≤ 1 for stability.\n\n**Key_Mechanism**:  \n- The Householder product structure enables efficient, stable, and tunable mixing of channel and token information, allowing the model to represent complex sequential transformations (e.g., arbitrary permutations, rotations) while maintaining computational tractability and stability.\n- Increasing nh directly enhances the model’s ability to capture higher-order dependencies, solve complex state-tracking tasks, and generalize to longer sequences.\n\n**Mathematical_Formulation**:  \n- For each token \\( x_i \\), generate nh keys \\( k_{i,j} \\), values \\( v_{i,j} \\), and step sizes \\( \\beta_{i,j} \\) via learned projections and nonlinearities.\n- Sequentially update the hidden state:\n  \\[\n  H_{i,0} = H_{i-1}\n  \\]\n  \\[\n  H_{i,j} = (I - \\beta_{i,j} k_{i,j} k_{i,j}^\\top) H_{i,j-1} + \\beta_{i,j} k_{i,j} v_{i,j}^\\top\n  \\]\n  \\[\n  H_i = H_{i,n_h}\n  \\]\n- Output is computed as usual: \\( \\hat{y}_i = \\text{dec}(H_i, x_i) \\).\n\n**Computational_Properties**:  \n- Time and memory complexity scale linearly with nh (O(nh d^2), where d is hidden size), but remain parallelizable across batch and sequence (using chunkwise or scan-based methods).\n- The spectral norm constraint ensures numerical stability on long sequences.\n- Training throughput decreases linearly with nh, but the model retains high efficiency compared to full dense RNNs or Transformers.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- In a linear RNN block (e.g., as used in Mamba/DeltaNet), replace the state-transition matrix computation with the product of nh Householder matrices per token, as described above.\n- Reuse existing DeltaNet/GLA/linear RNN recurrence code, but expand the key/value/beta generation and recurrence loop to handle nh sequential steps per token.\n- For gating (optional), multiply the final state-transition matrix by a learned forget gate per token.\n\n**Parameter_Settings**:  \n- nh (number of Householder products) is the main expressivity knob:  \n  - nh=1 recovers DeltaNet; nh=2–4 is often sufficient for substantial gains; nh=d (hidden size) approaches full expressivity.\n- Keys should be L2-normalized; betas in [0,1] (or [0,2] for negative eigenvalues/reflections); use SiLU or similar nonlinearity for projections.\n- Initialization as in DeltaNet/GLA; ensure spectral norm ≤ 1 (e.g., via parameterization or clipping).\n- For parameter-matching with baseline models, adjust head dimension or number of heads as needed.\n\n**Application_Conditions**:  \n- Apply when:  \n  - Model exhibits poor length extrapolation, unstable hidden state rank, or struggles with tasks requiring long-range state-tracking, narrative coherence, or complex reasoning.\n  - Training loss increases or plateaus on longer sequences.\n  - Need to balance computational efficiency with increased expressivity (e.g., for large-scale, long-context LLMs).\n- Not needed for purely local or short-context tasks.\n\n**Expected_Outcomes**:  \n- Improved performance on tasks requiring long-range context, narrative flow, and complex state-tracking (lambada_openai, hellaswag, winogrande, arc_easy/challenge, openbookqa, squad_completion).\n- Smoother, more stable training loss and improved length extrapolation.\n- Retained or slightly improved performance on local-context tasks (swde, piqa), with minimal computational overhead versus dense RNNs."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_MEDIUM: Enhanced State Forgetting and Information Density Control via Multi-Step Householder Updates", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**:  \n- Models with higher nh exhibit stable or decaying effective rank of the hidden state across long sequences, preventing accumulation of stale or irrelevant information.\n- This enhanced forgetting mechanism yields better length generalization (lower extrapolation loss on lambada_openai, hellaswag, triviaqa, codeparrot) and more robust performance on tasks with sequential resets or topic shifts (e.g., squad_completion, arc_challenge).\n- Training loss does not spike or degrade as sequence length exceeds training context.\n\n**Architectural_Symptoms**:  \n- Hidden state effective rank resets or decays at natural sequence boundaries (e.g., BOS tokens), as seen in information density plots.", "BACKGROUND": "**Title**: DeltaProduct: Improving State-Tracking in Linear RNNs via Householder Products\n\n**Historical Technical Context**: Prior to DeltaProduct, sequence modeling was dominated by architectures like RNNs and LSTMs, which processed data sequentially, and Transformers, which used self-attention for parallelism but suffered from quadratic complexity. Linear RNNs emerged as efficient alternatives, with models such as Mamba and GLA using diagonal state-transition matrices for fast, linear-time inference, but limited in expressivity. Recent advances included DeltaNet and RWKV-7, which introduced diagonal plus rank-1 state-transition matrices to improve token and channel mixing.\n\n**Technical Limitations**: Diagonal state-transition matrices in linear RNNs enable efficient computation but restrict the model’s ability to capture complex dependencies and solve challenging state-tracking tasks, such as group word problems. Even with diagonal plus rank-1 updates, models like DeltaNet are limited in expressivity, often requiring multiple layers to approximate more complex operations. Full, dense matrices offer maximal expressivity but are computationally prohibitive and unstable for long sequences.\n\n**Paper Concepts**: - **Generalized Householder Transformation**: A matrix of the form \\( I - \\beta k k^\\top \\), where \\( k \\) is a unit vector and \\( \\beta \\) controls the transformation, used to update the hidden state in a stable, norm-bounded way.\n- **DeltaProduct**: A linear RNN architecture whose state-transition matrix is a product of \\( n_h \\) generalized Householder transformations, yielding a diagonal plus rank-\\( n_h \\) structure that balances expressivity and efficiency.\n- **State-Tracking**: The model’s ability to track the evolution of a hidden state under a sequence of updates, often formalized as solving monoid or group word problems (e.g., permutation groups \\( S_n \\)).\n- **Length Extrapolation**: The model’s ability to generalize to sequences longer than those seen during training, crucial for robust language modeling.\n\n**Experimental Context**: The paper evaluates models on tasks requiring state-tracking (such as group word problems), language modeling, and reasoning, emphasizing the ability to maintain and update hidden states over long sequences. Evaluation philosophy focuses on both in-distribution performance and out-of-distribution generalization, particularly length extrapolation. Metrics include accuracy on sequence-based reasoning and cross-entropy loss for language generation and comprehension.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**:  \n- By stacking multiple Householder updates per token, DeltaProduct enables the hidden state to rapidly “forget” or overwrite prior information, as each Householder can implement a reflection or rotation that zeros out or mixes previous state dimensions.\n- The recurrence can thus implement complex, structured resets or state compressions, unlike rank-1 or diagonal-only updates which require more steps or layers to achieve similar forgetting.\n\n**Key_Mechanism**:  \n- The ability to perform multi-dimensional reflections/rotations per token allows the model to clear out or reorient information in the hidden state efficiently, supporting both rapid adaptation to new input and preservation of salient features over long sequences.\n\n**Mathematical_Formulation**:  \n- The product of Householder matrices can be designed (via learned keys and betas) to project the hidden state onto lower-dimensional subspaces or rotate it, effectively controlling the information density (rank) of the hidden state.\n\n**Computational_Properties**:  \n- No additional memory overhead beyond storing nh keys/values per token.\n- The spectral norm constraint prevents runaway amplification or vanishing of state, supporting stable training on long contexts.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- Monitor the effective rank of the hidden state during training and evaluation.\n- Increase nh if hidden state rank grows uncontrollably or fails to reset at logical sequence boundaries, or if length extrapolation is poor.\n- Optionally, combine with gating mechanisms for even finer control over state resets.\n\n**Parameter_Settings**:  \n- For tasks with frequent context switches or resets (e.g., multi-turn QA, document-level modeling), prefer nh ≥ 2.\n- Adjust the nonlinearity and beta parameterization to ensure the model can implement both reflections (for resets) and rotations (for information mixing).\n\n**Application_Conditions**:  \n- Especially beneficial for models deployed on tasks with long, multi-topic, or multi-turn sequences, or where rapid adaptation to new context is critical.\n\n**Expected_Outcomes**:  \n- More robust extrapolation to longer sequences, reduced catastrophic forgetting, and improved performance on tasks with context or topic shifts.\n- Training remains stable and efficient, with the hidden state dynamically adapting its information density as needed."}]