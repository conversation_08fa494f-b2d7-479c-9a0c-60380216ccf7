[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_HIGH: Data-Dependent Tempered Selection (DDTS) for Recurrent Linear Attention\n\nRodimus introduces a novel Data-Dependent Tempered Selection (DDTS) mechanism for recurrent linear attention. DDTS dynamically filters and compresses historical context into a fixed-size hidden state by using input-driven gates and temperature scaling, allowing the model to selectively retain or forget information at each timestep. This enables efficient O(1) per-token generation while maintaining or even improving language modeling performance compared to both traditional linear attention and softmax attention models.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Expect smoother and lower training loss curves, especially at longer sequence lengths, indicating improved memory efficiency and information retention.\n- Enhanced performance on tasks requiring long-range context and recall (lambada_openai, squad_completion, needlebench), as well as stable or improved results on reasoning and commonsense tasks (boolq, arc_easy/challenge, piqa, social_iqa).\n- No degradation, and sometimes improvement, on local-context tasks (hellaswag, winogrande) due to better semantic compression.\n\n**Architectural_Symptoms**: \n- Models with DDTS exhibit smaller hidden state sizes for equivalent or better performance, and faster convergence due to effective gating and dynamic forgetting.", "BACKGROUND": "**Title**: Rodimus*: Breaking the Accuracy-Efficiency Trade-Off with Efficient Attentions\n\n**Historical Technical Context**: Prior to this work, large language models primarily relied on Transformer architectures, which use softmax-based multi-head self-attention to capture dependencies across sequences. Earlier models like RNNs and LSTMs processed tokens sequentially with hidden states, while CNNs and initial linear state-space models introduced alternative mechanisms for sequence modeling. Recent research explored linear attention and recurrent models to address the growing computational costs of standard attention in long-context settings.\n\n**Technical Limitations**: Traditional softmax attention in Transformers incurs O(T) time and memory complexity per generated token due to the need to cache all previous key-value pairs, limiting efficiency for long sequences. Linear attention and recurrent models reduce complexity to O(1) but typically suffer from reduced capacity and performance due to fixed-size hidden states and insufficient mechanisms for selectively retaining relevant information. Prior compression approaches—semantic, token, and head compression—either lose information or struggle to match the accuracy of full-attention models.\n\n**Paper Concepts**: - **Data-Dependent Tempered Selection (DDTS):** A recurrent gating mechanism that dynamically filters and compresses input information into fixed-size hidden states using input-dependent gates and temperature scaling.\n- **Semantic Compression:** The process of summarizing historical context into a compact recurrent state, typically via linear attention or state-space models, with update equations like \\( S_t = A_t \\odot S_{t-1} + B_t \\odot u_t \\).\n- **Sliding Window Shared-Key Attention (SW-SKA):** A hybrid attention mechanism that combines sliding window token selection with shared-key compression across heads, reducing memory while maintaining expressiveness.\n- **Head Compression:** Techniques such as Shared-Key Attention (SKA) that reduce memory by sharing key representations across attention heads without significant loss of modeling power.\n\n**Experimental Context**: The paper evaluates models on core language modeling tasks, including next-token prediction and sequence generation, as well as challenging reasoning and recall-intensive tasks such as commonsense inference, reading comprehension, and question answering. Evaluation emphasizes both accuracy and computational efficiency, focusing on the trade-off between performance and resource usage across a range of model sizes and task types.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- At each timestep, the hidden state is updated via a data-dependent gating mechanism:  \n  \\( S_t = \\exp(-g_t \\cdot \\tau_t)^\\top 1_m \\odot S_{t-1} + (g_t^{\\tau_t})^\\top \\hat{\\beta}_t \\odot (k_t^\\top v_t) \\)  \n  where \\( g_t \\) (selection gate) and \\( \\tau_t \\) (temperature) are functions of the current input, and \\( \\hat{\\beta}_t \\) is a low-rank, input-dependent gate on the value channel.\n\n**Key_Mechanism**: \n- The combination of input-driven gating and temperature scaling allows the model to adaptively control the trade-off between retaining useful historical information and aggressively forgetting irrelevant context, directly addressing the \"attention dilution\" problem in linear attention.\n\n**Mathematical_Formulation**: \n- \\( g_t = \\zeta(x_t W_g + b_g) \\)\n- \\( \\tau_t = \\sigma(x_t W_\\tau + b_\\tau) \\)\n- \\( \\alpha_t = \\exp(-g_t \\odot \\tau_t) \\)\n- \\( \\hat{\\alpha}_t = g_t^{\\tau_t} \\)\n- \\( \\hat{\\beta}_t = \\sigma(x_t W_1^{\\hat{\\beta}} W_2^{\\hat{\\beta}} + b^{\\hat{\\beta}}) \\) (low-rank)\n- Hidden state update:  \n  \\( S_t = \\alpha_t^\\top 1_m \\odot S_{t-1} + \\hat{\\alpha}_t^\\top \\hat{\\beta}_t \\odot (k_t^\\top v_t) \\)\n\n**Computational_Properties**: \n- O(1) per-token generation (recurrent update, no growing cache).\n- Parallelizable during training via chunkwise processing.\n- Memory footprint significantly reduced compared to softmax attention and even prior linear attention models.\n- Efficient on GPU/TPU due to compatibility with parallel formats and reduced I/O.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- Replace the standard attention block in transformer layers with the Rodimus block (SSM-based), inserting DDTS gating (g_t, τ_t, \\(\\hat{\\beta}_t\\)) into the recurrent hidden state update.\n- Maintain existing embedding, normalization, and FFN structure; optionally integrate with GLU for compact token/channel mixing.\n\n**Parameter_Settings**: \n- State dimension n: can be set lower than in prior linear attention models (e.g., half of Mamba2) due to improved efficiency.\n- Temperature gate τ_t: learnable per layer, initialized to moderate values (e.g., 0.5–1.0).\n- Low-rank dimension ℓ for \\(\\hat{\\beta}_t\\): typically 8–32, tuned for parameter efficiency.\n- Employ ShortConv before gating to enhance local context aggregation.\n\n**Application_Conditions**: \n- Most beneficial when models are memory- or compute-constrained, or when long-context recall is a bottleneck.\n- Particularly effective for tasks with long-range dependencies and recall (lambada_openai, squad_completion, needlebench, fda, swde).\n- Can be used as a drop-in replacement for linear attention in SSM/RNN-based LLMs.\n\n**Expected_Outcomes**: \n- Improved or maintained performance on language modeling (lower training loss, better PPL).\n- Enhanced scores on context/recall and reasoning tasks without sacrificing efficiency.\n- Lower memory usage and faster inference/training for long sequences."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_HIGH: Hybrid Attention Block with Sliding Window Shared-Key Attention (SW-SKA)\n\nRodimus+ combines the global semantic compression of the Rodimus recurrent block with a novel local attention mechanism, Sliding Window Shared-Key Attention (SW-SKA). SW-SKA achieves token and head compression by (1) restricting attention to a local window and (2) using a single shared key across all heads, preserving multi-value expressiveness while reducing KV cache size losslessly.", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**: \n- Notable improvements in tasks requiring both global and local context (lambada_openai, squad_completion, hellaswag, winogrande).\n- Consistent or improved scores on structured extraction and recall (swde, fda, needlebench), due to efficient memory and attention localization.\n- Training loss curves remain smooth or improve, even as model size and sequence length increase, reflecting superior scaling properties.\n\n**Architectural_Symptoms**: \n- Models with SW-SKA show reduced KV cache memory consumption and maintain or improve performance as depth and parameter count increase.", "BACKGROUND": "**Title**: Rodimus*: Breaking the Accuracy-Efficiency Trade-Off with Efficient Attentions\n\n**Historical Technical Context**: Prior to this work, large language models primarily relied on Transformer architectures, which use softmax-based multi-head self-attention to capture dependencies across sequences. Earlier models like RNNs and LSTMs processed tokens sequentially with hidden states, while CNNs and initial linear state-space models introduced alternative mechanisms for sequence modeling. Recent research explored linear attention and recurrent models to address the growing computational costs of standard attention in long-context settings.\n\n**Technical Limitations**: Traditional softmax attention in Transformers incurs O(T) time and memory complexity per generated token due to the need to cache all previous key-value pairs, limiting efficiency for long sequences. Linear attention and recurrent models reduce complexity to O(1) but typically suffer from reduced capacity and performance due to fixed-size hidden states and insufficient mechanisms for selectively retaining relevant information. Prior compression approaches—semantic, token, and head compression—either lose information or struggle to match the accuracy of full-attention models.\n\n**Paper Concepts**: - **Data-Dependent Tempered Selection (DDTS):** A recurrent gating mechanism that dynamically filters and compresses input information into fixed-size hidden states using input-dependent gates and temperature scaling.\n- **Semantic Compression:** The process of summarizing historical context into a compact recurrent state, typically via linear attention or state-space models, with update equations like \\( S_t = A_t \\odot S_{t-1} + B_t \\odot u_t \\).\n- **Sliding Window Shared-Key Attention (SW-SKA):** A hybrid attention mechanism that combines sliding window token selection with shared-key compression across heads, reducing memory while maintaining expressiveness.\n- **Head Compression:** Techniques such as Shared-Key Attention (SKA) that reduce memory by sharing key representations across attention heads without significant loss of modeling power.\n\n**Experimental Context**: The paper evaluates models on core language modeling tasks, including next-token prediction and sequence generation, as well as challenging reasoning and recall-intensive tasks such as commonsense inference, reading comprehension, and question answering. Evaluation emphasizes both accuracy and computational efficiency, focusing on the trade-off between performance and resource usage across a range of model sizes and task types.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**: \n- The hybrid block first processes input with the Rodimus recurrent block (global semantic state), then passes the output to SW-SKA, which performs local self-attention within a sliding window, using a single shared key for all heads but keeping values head-specific.\n\n**Key_Mechanism**: \n- SW-SKA preserves the expressiveness of multi-head attention by only sharing keys (not values), avoiding the lossy compression of GQA/MQA, and leverages the empirical observation that most attention is local, thus focusing compute/memory on the most relevant tokens.\n\n**Mathematical_Formulation**: \n- For each attention head h:  \n  \\( O^h = \\text{softmax}((Q^h K^\\top) \\odot M) V^h \\),  \n  where \\( K \\) is shared across heads, \\( V^h \\) remains per-head, and \\( M \\) is a sliding window mask.\n- Residual connections:  \n  \\( X_{\\text{state}} = \\text{Rodimus}(\\text{Norm}(X)) + X \\)  \n  \\( \\hat{Y} = \\text{SW-SKA}(\\text{Norm}(X_{\\text{state}})) + X_{\\text{state}} \\)  \n  \\( Y = \\text{FFN}(\\text{Norm}(\\hat{Y})) + X_{\\text{state}} \\)\n\n**Computational_Properties**: \n- O(1) memory per token for global context, O(window size) for local attention.\n- KV cache size reduced by a constant factor (number of heads), with no loss in value diversity.\n- Compatible with standard parallelization and chunkwise training.\n- Scales efficiently with model size and sequence length.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**: \n- Stack Rodimus blocks with SW-SKA and GLU-based FFN in each transformer layer.\n- Insert SW-SKA after the Rodimus block, using two-hop residual connections to maintain separation of global and local context.\n- Replace standard MHA with SW-SKA by sharing keys across heads and restricting attention to a sliding window.\n\n**Parameter_Settings**: \n- Window size: tune based on task context length (typical range: 32–512 tokens).\n- Shared key dimension: match per-head key size for compatibility.\n- Maintain head-specific values for expressiveness.\n\n**Application_Conditions**: \n- Use in models where both long-range (global) and short-range (local) dependencies are important.\n- Particularly valuable for large models or long sequence tasks where KV cache size is a bottleneck.\n- Suitable for both pretraining and finetuning, especially for downstream tasks combining recall, reasoning, and local context.\n\n**Expected_Outcomes**: \n- Improved scaling curves: as model size or context increases, performance is maintained or improved without increased memory cost.\n- Better task performance for context-sensitive, reasoning, and recall-intensive benchmarks (lambada_openai, squad_completion, hellaswag, winogrande, needlebench).\n- Maintains or improves performance on structured extraction (swde) and adaptation tasks (fda)."}]