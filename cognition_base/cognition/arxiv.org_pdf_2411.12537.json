[{"DESIGN_INSIGHT": "### DESIGN_INSIGHT_1: Extending LRNN State-Transition Eigenvalue Range to [−1, 1] Enables State-Tracking and Regular Language Recognition", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**:  \n- Models with LRNN (e.g., Mamba, DeltaNet) layers whose state-transition matrices permit eigenvalues in [−1, 1] (rather than [0, 1]) will show dramatic improvements on tasks requiring explicit state-tracking—such as code evaluation, modular arithmetic, and parity (e.g., improved piqa, social_iqa, and especially code/math benchmarks).  \n- Expect smoother and lower training loss curves on tasks with sequential dependencies, and improved length generalization for state-tracking tasks (e.g., lambada_openai, hellaswag, and possibly squad_completion).  \n- Gains are most pronounced in non-diagonal LRNNs (e.g., DeltaNet), with diagonal LRNNs benefiting primarily on simpler state-tracking tasks (e.g., parity).\n\n**Architectural_Symptoms**:  \n- Models previously plateauing at random-guess accuracy on parity/state-tracking benchmarks will reach near-perfect accuracy after this modification, with no loss of training stability or efficiency.", "BACKGROUND": "**Title**: Unlocking State-Tracking in Linear RNNs Through Negative Eigenvalues\n\n**Historical Technical Context**: Before this work, sequence modeling was dominated by architectures like RNNs, LSTMs, and Transformers. RNNs and LSTMs used recurrent hidden states and non-linearities to process sequences, while Transformers relied on attention mechanisms for parallel processing. Recently, Linear RNNs (LRNNs) such as Mamba and DeltaNet emerged, using structured, often diagonal, state-transition matrices for efficient, linear-scaling sequence modeling.\n\n**Technical Limitations**: Despite their efficiency, both Transformers and LRNNs with positive-only eigenvalues in their state-transition matrices struggled with state-tracking tasks, such as computing sequence parity or modular counting, which non-linear RNNs could solve. This limitation arose because restricting eigenvalues to [0, 1] prevented the models from representing the necessary periodic or alternating state transitions. As a result, these models could not implement simple finite-state automata in a single forward pass, limiting their expressivity on tasks requiring internal memory.\n\n**Paper Concepts**: - **Linear RNN (LRNN):** A neural network where the hidden state update is a linear function: \\( H_t = A(x_t) H_{t-1} + B(x_t) \\), with structured, learnable matrices \\( A \\), \\( B \\).\n- **Eigenvalue Range:** The set of possible eigenvalues of the state-transition matrix \\( A \\); extending this from \\([0,1]\\) to \\([-1,1]\\) enables richer state dynamics.\n- **State-Tracking:** The ability of a model to maintain and update an internal state representing the history of the sequence, essential for tasks like parity or modular counting.\n- **Generalized Householder (GH) Matrix:** A matrix of the form \\( I - \\beta v v^\\top \\) (with \\( \\|v\\|=1 \\)), used for non-diagonal state transitions, allowing more expressive token mixing.\n- **Regular Language Recognition:** The capability to implement any finite-state automaton, enabling the model to recognize languages defined by regular expressions or automata.\n\n**Experimental Context**: The paper evaluates models on tasks requiring state-tracking, such as parity computation, modular arithmetic, and group word problems, as well as standard language modeling. Evaluations emphasize the ability to generalize to longer sequences and measure performance on reasoning, code, math, and language generation tasks. The experimental philosophy focuses on understanding both theoretical expressivity and practical performance in scenarios demanding sequence memory and internal state updates.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**:  \n- Replace state-transition matrices in LRNN layers (both diagonal and generalized Householder/non-diagonal) so that their eigenvalues are mapped to [−1, 1] rather than [0, 1]. For diagonal matrices, this means applying $A(x) = \\mathrm{Diag}(2s(x) - 1)$ for $s(x) \\in [0, 1]^n$; for GH matrices, use $A(x) = I - 2\\phi(x)v(x)v(x)^\\top$ for $\\phi(x) \\in [0, 1]$ and $\\|v(x)\\| = 1$.\n\n**Key_Mechanism**:  \n- Allowing negative eigenvalues enables the LRNN to alternate or oscillate internal states, which is essential for encoding finite-state automata (e.g., parity, modular counting, and general regular languages). This overcomes the convergence/degeneracy that occurs when all eigenvalues are positive, which otherwise prevents the model from tracking state transitions over long sequences.\n\n**Mathematical_Formulation**:  \n- For diagonal LRNNs: $A^{-}_{\\text{diag}}(x) = \\mathrm{Diag}(2s(x) - 1)$  \n- For GH LRNNs: $A^{-}_{\\text{GH}}(x) = I - 2\\phi(x)v(x)v(x)^\\top$  \n- This ensures all eigenvalues lie in $[-1, 1]$; the matrix norm remains $\\leq 1$ for stability.\n\n**Computational_Properties**:  \n- No additional time or space complexity compared to standard LRNNs; efficient matrix-vector products are preserved.  \n- No degradation in training stability or convergence observed; models can be trained at scale (1.3B+ parameters) with standard optimizers and learning rates.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- Modify the computation of the state-transition matrix in each LRNN layer:  \n  - For diagonal LRNNs (e.g., Mamba): replace $A(x) = \\mathrm{Diag}(s(x))$ with $A(x) = \\mathrm{Diag}(2s(x) - 1)$.  \n  - For GH/non-diagonal LRNNs (e.g., DeltaNet): replace $A(x) = I - \\phi(x)v(x)v(x)^\\top$ with $A(x) = I - 2\\phi(x)v(x)v(x)^\\top$.\n- Ensure downstream layers and normalization remain compatible with the new value range.\n\n**Parameter_Settings**:  \n- $s(x), \\phi(x)$ remain in $[0, 1]$ via sigmoid or softplus activations; only the mapping to $[-1, 1]$ changes.  \n- Initialization and learning rates can remain as in the original LRNNs; no special tuning required for stability.\n\n**Application_Conditions**:  \n- Apply whenever improved state-tracking, regular language recognition, or code/math reasoning is a priority (i.e., if performance is poor on code, math, or state-dependent QA tasks such as boolq, arc_easy/challenge, openbookqa).\n- Particularly valuable for non-diagonal LRNNs and for tasks with long-range sequential dependencies.\n\n**Expected_Outcomes**:  \n- Significant improvements on state-tracking and regular language tasks (e.g., code/math QA, parity, modular counting);  \n- Smoother and more rapid decrease in training loss on such tasks;  \n- Comparable or slightly improved performance on standard language modeling (lambada_openai, hellaswag, winogrande, squad_completion), with no loss of efficiency or stability."}, {"DESIGN_INSIGHT": "### DESIGN_INSIGHT_2: Use Non-Diagonal (Generalized Householder) State-Transition Matrices for Simultaneous Token-Channel Mixing and Enhanced Expressivity", "EXPERIMENTAL_TRIGGER_PATTERNS": "**Task_Performance_Signatures**:  \n- Non-diagonal LRNNs (e.g., DeltaNet) with eigenvalues in [−1, 1] will outperform diagonal LRNNs (e.g., Mamba) on tasks requiring complex state-tracking, modular arithmetic, and group word problems (notably code, math, and structured reasoning tasks: arc_easy/challenge, openbookqa, swde).\n- Expect improved length generalization on state-tracking tasks and greater robustness to sequence structure; may see increased gains on code and math datasets compared to narrative-only tasks.\n\n**Architectural_Symptoms**:  \n- Models with non-diagonal state-transitions will show higher accuracy on synthetic automata/group tasks and reduced perplexity on code/math benchmarks, especially as model size increases.", "BACKGROUND": "**Title**: Unlocking State-Tracking in Linear RNNs Through Negative Eigenvalues\n\n**Historical Technical Context**: Before this work, sequence modeling was dominated by architectures like RNNs, LSTMs, and Transformers. RNNs and LSTMs used recurrent hidden states and non-linearities to process sequences, while Transformers relied on attention mechanisms for parallel processing. Recently, Linear RNNs (LRNNs) such as Mamba and DeltaNet emerged, using structured, often diagonal, state-transition matrices for efficient, linear-scaling sequence modeling.\n\n**Technical Limitations**: Despite their efficiency, both Transformers and LRNNs with positive-only eigenvalues in their state-transition matrices struggled with state-tracking tasks, such as computing sequence parity or modular counting, which non-linear RNNs could solve. This limitation arose because restricting eigenvalues to [0, 1] prevented the models from representing the necessary periodic or alternating state transitions. As a result, these models could not implement simple finite-state automata in a single forward pass, limiting their expressivity on tasks requiring internal memory.\n\n**Paper Concepts**: - **Linear RNN (LRNN):** A neural network where the hidden state update is a linear function: \\( H_t = A(x_t) H_{t-1} + B(x_t) \\), with structured, learnable matrices \\( A \\), \\( B \\).\n- **Eigenvalue Range:** The set of possible eigenvalues of the state-transition matrix \\( A \\); extending this from \\([0,1]\\) to \\([-1,1]\\) enables richer state dynamics.\n- **State-Tracking:** The ability of a model to maintain and update an internal state representing the history of the sequence, essential for tasks like parity or modular counting.\n- **Generalized Householder (GH) Matrix:** A matrix of the form \\( I - \\beta v v^\\top \\) (with \\( \\|v\\|=1 \\)), used for non-diagonal state transitions, allowing more expressive token mixing.\n- **Regular Language Recognition:** The capability to implement any finite-state automaton, enabling the model to recognize languages defined by regular expressions or automata.\n\n**Experimental Context**: The paper evaluates models on tasks requiring state-tracking, such as parity computation, modular arithmetic, and group word problems, as well as standard language modeling. Evaluations emphasize the ability to generalize to longer sequences and measure performance on reasoning, code, math, and language generation tasks. The experimental philosophy focuses on understanding both theoretical expressivity and practical performance in scenarios demanding sequence memory and internal state updates.", "ALGORITHMIC_INNOVATION": "**Core_Algorithm**:  \n- Employ state-transition matrices as products of generalized Householder (GH) matrices, $A(x) = \\prod_{i=1}^k (I - 2\\phi_i(x)v_i(x)v_i(x)^\\top)$, with each $\\phi_i(x) \\in [0, 1]$, $\\|v_i(x)\\|=1$, and $k$ controlling expressivity.\n- For DeltaNet, $k=1$ suffices for many tasks; higher $k$ increases the class of regular languages representable.\n\n**Key_Mechanism**:  \n- Non-diagonal transitions allow the LRNN to mix information both across tokens and channels, enabling it to represent complex state transitions (e.g., permutations, modular counting, and group operations) that are impossible with diagonal-only transitions.\n\n**Mathematical_Formulation**:  \n- $A(x) \\in M^n_k([−1,1])$, where $M^n_k([−1,1])$ is the set of $n \\times n$ matrices expressible as products of $k$ GH matrices with eigenvalues in $[−1, 1]$.\n- For group word problems, $k$ can be set according to the group's structure (e.g., $k = n-1$ for $S_n$).\n\n**Computational_Properties**:  \n- Each additional GH factor increases computational cost linearly in $k$; for $k=1$ (DeltaNet), cost is comparable to diagonal LRNNs.\n- Matrix norm remains $\\leq 1$ for stability; efficient parallelization and hardware compatibility are preserved.", "IMPLEMENTATION_GUIDANCE": "**Integration_Strategy**:  \n- Replace or augment diagonal state-transition matrices with GH matrices in LRNN layers, especially for models targeting code, math, or structured reasoning tasks.\n- For maximal expressivity, allow stacking or composition of multiple GH matrices ($k > 1$) in the state-transition step.\n\n**Parameter_Settings**:  \n- Set $\\phi(x)$ via sigmoid activations to $[0, 1]$; $v(x)$ normalized to unit norm.\n- For most practical tasks, $k=1$ suffices; increase $k$ only if evidence of underfitting on complex group/automata tasks.\n\n**Application_Conditions**:  \n- Use for models where code, math, or structured data extraction is a priority, or where diagonal LRNNs plateau on state-tracking tasks.\n- Particularly beneficial at larger model scales and when training on datasets with strong sequential or algorithmic structure.\n\n**Expected_Outcomes**:  \n- Substantial performance gains on code, math, and group word problem tasks (arc_easy/challenge, openbookqa, swde, code/math perplexity);  \n- Comparable or slightly improved performance on standard language modeling and narrative tasks;  \n- Maintains training efficiency and stability at scale."}]