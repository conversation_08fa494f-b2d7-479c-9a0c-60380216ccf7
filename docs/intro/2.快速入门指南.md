# ASI-Arch 快速入门指南

## 系统要求

### 硬件要求
- **CPU**: Intel/AMD x64架构
- **内存**: 最低16GB，推荐32GB
- **GPU**: CUDA兼容GPU（推荐）
- **存储**: 最低50GB空闲容量

### 软件要求
- **Python**: 3.8以上（推荐3.10）
- **MongoDB**: 4.4以上
- **Docker & Docker Compose**: 最新版
- **CUDA**: GPU使用时

## 安装步骤

### 1. 克隆仓库
```bash
git clone https://github.com/GAIR-NLP/ASI-Arch.git
cd ASI-Arch
```

### 2. 创建Conda环境
```bash
conda create -n asi-arch python=3.10
conda activate asi-arch
```

### 3. 安装依赖项
```bash
# 主要要求
pip install -r requirements.txt

# PyTorch（CUDA支持）
pip3 install torch==2.4.0 --index-url https://download.pytorch.org/whl/cu124

# 组件特定要求
pip install -r database/requirements.txt
pip install -r cognition_base/requirements.txt
```

## 服务启动

### 终端1: 数据库服务
```bash
cd database
docker-compose up -d
./start_api.sh
```

### 终端2: 认知基础服务
```bash
cd cognition_base
docker-compose up -d
python rag_api.py
```

### 终端3: 主管道
```bash
conda activate asi-arch
cd pipeline
python pipeline.py
```

## 服务确认

### 健康检查
```bash
# 数据库API
curl http://localhost:8001/health

# 认知基础API
curl http://localhost:5000/health
```

### 管理界面
- **MongoDB管理**: http://localhost:8081
- **OpenSearch仪表板**: http://localhost:5601

## 基本使用方法

### 执行单次实验循环
```bash
cd pipeline
python -c "import asyncio; from pipeline import run_single_experiment; asyncio.run(run_single_experiment())"
```

### 日志监控
- **调试日志**: `pipeline/files/debug/`
- **实验结果**: 配置的CSV文件
- **性能指标**: 数据库内

## 故障排除

### 常见问题

#### Docker服务无法启动
```bash
# Docker守护进程确认
sudo systemctl status docker

# 端口冲突确认
netstat -tulpn | grep :8001
netstat -tulpn | grep :5000
```

#### MongoDB连接错误
```bash
# MongoDB 容器状态确认
docker ps | grep mongo

# 日志确认
docker logs asi-arch-mongodb-1
```

#### CUDA相关错误
```bash
# CUDA可用性确认
python -c "import torch; print(torch.cuda.is_available())"

# GPU信息确认
nvidia-smi
```

### 日志级别调整
在`pipeline/config.py`中可以调整日志级别：
```python
LOG_LEVEL = "DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

## 下一步

1. **系统概述理解**: `docs/intro/1.system-overview.md`
2. **架构详细**: `docs/intro/3.architecture-details.md`
3. **自定义指南**: `docs/intro/customization-guide.md`
4. **API 参考**: `docs/api/`

## 支持

如果遇到问题：
1. 检查日志文件
2. 检查服务状态
3. 在GitHub的Issues中报告
4. 参考文档的详细部分