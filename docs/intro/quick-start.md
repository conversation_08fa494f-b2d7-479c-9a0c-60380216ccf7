# ASI-Arch クイックスタートガイド

## システム要件

### ハードウェア要件
- **CPU**: Intel/AMD x64アーキテクチャ
- **メモリ**: 最小16GB、推奨32GB
- **GPU**: CUDA対応GPU（推奨）
- **ストレージ**: 最小50GB空き容量

### ソフトウェア要件
- **Python**: 3.8以上（推奨3.10）
- **MongoDB**: 4.4以上
- **Docker & Docker Compose**: 最新版
- **CUDA**: GPU使用時

## インストール手順

### 1. リポジトリクローン
```bash
git clone https://github.com/GAIR-NLP/ASI-Arch.git
cd ASI-Arch
```

### 2. Conda環境作成
```bash
conda create -n asi-arch python=3.10
conda activate asi-arch
```

### 3. 依存関係インストール
```bash
# メイン要件
pip install -r requirements.txt

# PyTorch（CUDA対応）
pip3 install torch==2.4.0 --index-url https://download.pytorch.org/whl/cu124

# コンポーネント固有要件
pip install -r database/requirements.txt
pip install -r cognition_base/requirements.txt
```

## サービス起動

### ターミナル1: データベースサービス
```bash
cd database
docker-compose up -d
./start_api.sh
```

### ターミナル2: 認知ベースサービス
```bash
cd cognition_base
docker-compose up -d
python rag_api.py
```

### ターミナル3: メインパイプライン
```bash
conda activate asi-arch
cd pipeline
python pipeline.py
```

## サービス確認

### ヘルスチェック
```bash
# データベースAPI
curl http://localhost:8001/health

# 認知ベースAPI
curl http://localhost:5000/health
```

### 管理インターフェース
- **MongoDB管理**: http://localhost:8081
- **OpenSearchダッシュボード**: http://localhost:5601

## 基本使用方法

### 単一実験サイクル実行
```bash
cd pipeline
python -c "import asyncio; from pipeline import run_single_experiment; asyncio.run(run_single_experiment())"
```

### ログ監視
- **デバッグログ**: `pipeline/files/debug/`
- **実験結果**: 設定されたCSVファイル
- **パフォーマンス指標**: データベース内

## トラブルシューティング

### よくある問題

#### Dockerサービスが起動しない
```bash
# Dockerデーモン確認
sudo systemctl status docker

# ポート競合確認
netstat -tulpn | grep :8001
netstat -tulpn | grep :5000
```

#### MongoDB接続エラー
```bash
# MongoDB コンテナ状態確認
docker ps | grep mongo

# ログ確認
docker logs asi-arch-mongodb-1
```

#### CUDA関連エラー
```bash
# CUDA可用性確認
python -c "import torch; print(torch.cuda.is_available())"

# GPU情報確認
nvidia-smi
```

### ログレベル調整
`pipeline/config.py`でログレベルを調整可能：
```python
LOG_LEVEL = "DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

## 次のステップ

1. **システム概要理解**: `docs/intro/system-overview.md`
2. **アーキテクチャ詳細**: `docs/intro/architecture-details.md`
3. **カスタマイゼーション**: `docs/intro/customization-guide.md`
4. **API リファレンス**: `docs/api/`

## サポート

問題が発生した場合：
1. ログファイルを確認
2. サービス状態をチェック
3. GitHubのIssuesで報告
4. ドキュメントの詳細セクションを参照